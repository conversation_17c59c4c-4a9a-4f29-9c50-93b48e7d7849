defmodule SampleApp.Relations.Comments do
  use Ecto.Schema
  import Ecto.Schema

  schema("comments") do
    field(:body, :string)
    field(:approved, :boolean, default: false)
    field(:user_id, :integer)
    field(:post_id, :integer)
    timestamps()
  end

  schema("comments") do
    field(:body, :string)
    field(:approved, :boolean, default: false)
    field(:user_id, :integer)
    field(:post_id, :integer)
    timestamps()
  end
end
