defmodule SampleApp.Relations.Posts do
  use Ecto.Schema
  import Ecto.Schema

  schema("posts") do
    field(:title, :string)
    field(:body, :string)
    field(:published, :boolean, default: false)
    field(:view_count, :integer, default: 0)
    field(:user_id, :integer)
    timestamps()
  end

  schema("posts") do
    field(:title, :string)
    field(:body, :string)
    field(:published, :boolean, default: false)
    field(:view_count, :integer, default: 0)
    field(:user_id, :integer)
    timestamps()
  end
end
