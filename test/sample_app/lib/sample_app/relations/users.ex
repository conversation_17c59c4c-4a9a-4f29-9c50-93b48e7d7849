defmodule SampleApp.Relations.Users do
  use Ecto.Schema
  import Ecto.Schema

  schema("users") do
    field(:email, :string)
    field(:first_name, :string)
    field(:last_name, :string)
    field(:age, :integer)
    field(:is_active, :boolean, default: true)
    field(:profile_data, :string)
    field(:tags, :string, default: "[]")
    field(:score, :decimal)
    field(:birth_date, :string)
    field(:last_login_at, :string)
    timestamps()
  end

  schema("users") do
    field(:email, :string)
    field(:first_name, :string)
    field(:last_name, :string)
    field(:age, :integer)
    field(:is_active, :boolean, default: true)
    field(:profile_data, :string)
    field(:tags, :string, default: "[]")
    field(:score, :decimal)
    field(:birth_date, :string)
    field(:last_login_at, :string)

    timestamps()
  end

  def foo, do: "bar"
end
